// 简单的 DES 加密解密例子
require('./encryption.js');

console.log('🔐 DES 加密解密例子');
console.log('='.repeat(30));

// 测试数据
const key = "mykey123";        // 密钥
const plaintext = "hello123";  // 原文

console.log('原文:', plaintext);
console.log('密钥:', key);

// 加密
console.log('\n🔒 加密中...');
const encrypted = xN[it][tn](key, plaintext);  // 使用加密接口
console.log('加密结果:', encrypted);

/*


// 解密
console.log('\n🔓 解密中...');
const decrypted = xN[it][qt](key, encrypted);  // 使用解密接口
console.log('解密结果:', decrypted);

// 验证
console.log('\n✅ 验证:');
console.log('原文 === 解密结果:', plaintext === decrypted);

console.log('\n='.repeat(30));
console.log('完成！');
*/
