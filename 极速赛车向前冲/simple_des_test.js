// 简单的 DES 加密函数测试
require('./encryption.js');

console.log('🔐 简单 DES 加密测试');
console.log('='.repeat(40));

// 测试 xN[it][dP] 函数
function testDESFunction() {
    try {
        console.log('\n📋 检查函数状态:');
        console.log('xN 对象:', typeof xN !== 'undefined' ? '✅ 存在' : '❌ 不存在');
        console.log('it 变量:', typeof it !== 'undefined' ? `✅ 值: "${it}"` : '❌ 不存在');
        console.log('dP 变量:', typeof dP !== 'undefined' ? `✅ 值: "${dP}"` : '❌ 不存在');
        
        if (typeof xN !== 'undefined' && typeof it !== 'undefined' && typeof dP !== 'undefined') {
            console.log('xN[it] 对象:', typeof xN[it] !== 'undefined' ? '✅ 存在' : '❌ 不存在');
            console.log('xN[it][dP] 函数:', typeof xN[it][dP] === 'function' ? '✅ 存在' : '❌ 不存在');
            
            if (typeof xN[it][dP] === 'function') {
                console.log('\n🎯 函数可以调用！');
                console.log('函数名称:', `xN["${it}"]["${dP}"]`);
                console.log('函数长度:', xN[it][dP].length, '个参数');
                
                // 尝试调用函数（使用简单的测试数据）
                console.log('\n🧪 尝试调用函数:');
                try {
                    // 准备测试数据
                    const testKey = "testkey8"; // 8字节密钥
                    const testData = "hello123"; // 8字节数据
                    const mode = 1; // 加密模式
                    
                    console.log('测试密钥:', testKey);
                    console.log('测试数据:', testData);
                    console.log('模式:', mode);
                    
                    // 调用函数
                    const result = xN[it][dP](testKey, testData, mode);
                    console.log('✅ 函数调用成功！');
                    console.log('返回结果类型:', typeof result);
                    console.log('返回结果:', result);
                    
                    return true;
                } catch (error) {
                    console.log('❌ 函数调用失败:', error.message);
                    console.log('这可能是因为缺少必要的变量或参数格式不正确');
                    return false;
                }
            } else {
                console.log('❌ 函数不存在');
                return false;
            }
        } else {
            console.log('❌ 基础对象或变量不存在');
            return false;
        }
    } catch (error) {
        console.log('❌ 测试过程出错:', error.message);
        return false;
    }
}

// 显示如何直接使用函数
function showUsageExample() {
    console.log('\n📖 使用示例:');
    console.log('```javascript');
    console.log('// 1. 确保已加载 encryption.js');
    console.log('require("./encryption.js");');
    console.log('');
    console.log('// 2. 直接调用加密函数');
    console.log('const key = "your8key";     // 8字节密钥');
    console.log('const data = "yourdata";    // 要加密的数据');
    console.log('const mode = 1;             // 1=加密, 0=解密');
    console.log('');
    console.log('// 3. 调用函数');
    console.log('const result = xN[it][dP](key, data, mode);');
    console.log('console.log("加密结果:", result);');
    console.log('```');
}

// 显示当前变量值
function showCurrentValues() {
    console.log('\n📊 当前变量值:');
    
    const vars = [
        { name: 'it', value: typeof it !== 'undefined' ? it : 'undefined' },
        { name: 'dP', value: typeof dP !== 'undefined' ? dP : 'undefined' },
        { name: 'hP', value: typeof hP !== 'undefined' ? hP : 'undefined' },
        { name: 'vP', value: typeof vP !== 'undefined' ? vP : 'undefined' },
        { name: 'tn', value: typeof tn !== 'undefined' ? tn : 'undefined' },
        { name: 'qt', value: typeof qt !== 'undefined' ? qt : 'undefined' }
    ];
    
    vars.forEach(v => {
        console.log(`${v.name}: "${v.value}"`);
    });
    
    // 显示一些 S-盒数据
    if (typeof gM !== 'undefined' && Array.isArray(gM) && gM.length > 520) {
        console.log('\n📈 S-盒数据样本:');
        console.log('gM[518] (bP):', gM[518]);
        console.log('gM[519] (gP):', gM[519]);
        console.log('gM[520] (pP):', gM[520]);
    }
}

// 运行测试
console.log('开始测试...');
const success = testDESFunction();

// showCurrentValues();
showUsageExample();

console.log('\n' + '='.repeat(40));
if (success) {
    console.log('🎉 测试成功！xN[it][dP] 函数可以使用。');
} else {
    console.log('⚠️  测试失败，需要检查变量初始化。');
}
console.log('='.repeat(40));
