// 测试 DES 加密功能
require('./encryption.js');

console.log('🔐 DES 加密功能测试');
console.log('='.repeat(50));

// 检查加密对象是否正确初始化
function checkEncryptionObjects() {
    console.log('\n📋 1. 检查加密对象初始化');
    
    try {
        console.log('xN 对象:', typeof xN !== 'undefined' ? '✅ 已定义' : '❌ 未定义');
        console.log('it 变量:', typeof it !== 'undefined' ? `✅ 值为: ${it}` : '❌ 未定义');
        console.log('xN[it] 对象:', typeof xN[it] !== 'undefined' ? '✅ 已定义' : '❌ 未定义');
        
        // 检查关键函数是否存在
        const functions = [
            { name: 'dP (主加密函数)', key: dP },
            { name: 'hP (Base64编码)', key: hP },
            { name: 'vP (Base64解码)', key: vP },
            { name: 'aT (密钥调度)', key: aT },
            { name: 'tn (加密接口)', key: tn },
            { name: 'qt (解密接口)', key: qt }
        ];
        
        functions.forEach(func => {
            if (typeof func.key !== 'undefined' && typeof xN[it][func.key] === 'function') {
                console.log(`${func.name}:`, '✅ 函数存在');
            } else {
                console.log(`${func.name}:`, '❌ 函数不存在');
            }
        });
        
        return true;
    } catch (error) {
        console.log('❌ 检查失败:', error.message);
        return false;
    }
}

// 测试 Base64 编码/解码
function testBase64() {
    console.log('\n📝 2. 测试 Base64 编码/解码');
    
    try {
        if (typeof xN[it][hP] === 'function' && typeof xN[it][vP] === 'function') {
            const testString = "Hello, DES!";
            
            // 创建一个临时对象来测试
            const testObj = {
                [hP]: xN[it][hP],
                [vP]: xN[it][vP],
                [typeof cP !== 'undefined' ? cP : '_stringToBytes']: function(str) {
                    // 简单的字符串转字节数组
                    const bytes = [];
                    for (let i = 0; i < str.length; i++) {
                        bytes.push(str.charCodeAt(i));
                    }
                    return bytes;
                }
            };
            
            console.log('Base64 编码函数:', '✅ 可用');
            console.log('Base64 解码函数:', '✅ 可用');
            
            return true;
        } else {
            console.log('❌ Base64 函数不可用');
            return false;
        }
    } catch (error) {
        console.log('❌ Base64 测试失败:', error.message);
        return false;
    }
}

// 测试 S-盒数据
function testSBoxData() {
    console.log('\n🔧 3. 测试 S-盒数据');
    
    try {
        // 检查 S-盒变量是否有值
        const sboxVars = [
            { name: 'bP', value: typeof bP !== 'undefined' ? bP : 'undefined' },
            { name: 'gP', value: typeof gP !== 'undefined' ? gP : 'undefined' },
            { name: 'pP', value: typeof pP !== 'undefined' ? pP : 'undefined' },
            { name: 'EP', value: typeof EP !== 'undefined' ? EP : 'undefined' },
            { name: 'yP', value: typeof yP !== 'undefined' ? yP : 'undefined' }
        ];
        
        let validCount = 0;
        sboxVars.forEach(svar => {
            if (svar.value !== 'undefined' && typeof svar.value === 'number') {
                console.log(`${svar.name}:`, `✅ ${svar.value}`);
                validCount++;
            } else {
                console.log(`${svar.name}:`, '❌ 未定义或无效');
            }
        });
        
        console.log(`S-盒数据状态: ${validCount}/${sboxVars.length} 个变量有效`);
        return validCount > 0;
    } catch (error) {
        console.log('❌ S-盒数据检查失败:', error.message);
        return false;
    }
}

// 测试 gM 数组
function testGMArray() {
    console.log('\n📊 4. 测试 gM 数组');
    
    try {
        if (typeof gM !== 'undefined' && Array.isArray(gM)) {
            console.log('gM 数组:', `✅ 长度为 ${gM.length}`);
            console.log('前10个值:', gM.slice(0, 10));
            console.log('gM[518] (bP应该等于):', gM[518]);
            console.log('gM[519] (gP应该等于):', gM[519]);
            console.log('gM[520] (pP应该等于):', gM[520]);
            return true;
        } else {
            console.log('❌ gM 数组未定义或不是数组');
            return false;
        }
    } catch (error) {
        console.log('❌ gM 数组测试失败:', error.message);
        return false;
    }
}

// 测试主加密函数结构
function testMainFunction() {
    console.log('\n🔐 5. 测试主加密函数');
    
    try {
        if (typeof xN[it][dP] === 'function') {
            console.log('主加密函数:', '✅ 存在');
            console.log('函数长度:', xN[it][dP].toString().length, '字符');
            
            // 尝试检查函数是否包含关键的 S-盒操作
            const funcStr = xN[it][dP].toString();
            const hasArrays = funcStr.includes('new Array');
            const hasBitOps = funcStr.includes('>>>') || funcStr.includes('<<');
            
            console.log('包含数组操作:', hasArrays ? '✅ 是' : '❌ 否');
            console.log('包含位运算:', hasBitOps ? '✅ 是' : '❌ 否');
            
            return true;
        } else {
            console.log('❌ 主加密函数不存在');
            return false;
        }
    } catch (error) {
        console.log('❌ 主加密函数测试失败:', error.message);
        return false;
    }
}

// 运行所有测试
function runAllTests() {
    console.log('开始运行所有测试...\n');
    
    const tests = [
        { name: '对象初始化检查', fn: checkEncryptionObjects },
        { name: 'Base64 功能测试', fn: testBase64 },
        { name: 'S-盒数据测试', fn: testSBoxData },
        { name: 'gM 数组测试', fn: testGMArray },
        { name: '主加密函数测试', fn: testMainFunction }
    ];
    
    const results = [];
    
    tests.forEach(test => {
        try {
            const result = test.fn();
            results.push({ name: test.name, success: result });
        } catch (error) {
            results.push({ name: test.name, success: false, error: error.message });
        }
    });
    
    // 输出测试总结
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试总结');
    console.log('='.repeat(50));
    
    const passed = results.filter(r => r.success).length;
    const total = results.length;
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${status} ${result.name}`);
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    console.log(`\n总计: ${passed}/${total} 测试通过`);
    
    if (passed === total) {
        console.log('🎉 所有测试都通过了！DES 加密库基本可用。');
        console.log('\n💡 下一步可以尝试：');
        console.log('   1. 提取完整的加密/解密接口函数');
        console.log('   2. 创建独立的 DES 加密模块');
        console.log('   3. 测试实际的加密/解密操作');
    } else {
        console.log('⚠️  部分测试失败，需要进一步调试。');
    }
}

// 运行测试
runAllTests();
